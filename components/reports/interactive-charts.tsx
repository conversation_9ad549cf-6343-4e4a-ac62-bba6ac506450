"use client";

import React from "react";
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  TrendingUp,
  TrendingDown,
  Calendar,
  Users,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Activity,
} from "lucide-react";
import { format } from "date-fns";
import { ar } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { useRTL } from "@/contexts/rtl-context";
import type {
  AttendanceTrendData,
  UserAttendanceAnalytics,
  CollegeComparisonData,
} from "@/lib/reports-analytics";

// Debug utility for chart data
const debugChartData = (componentName: string, data: any) => {
  if (process.env.NODE_ENV === "development") {
    console.log(`[${componentName}] Chart Data:`, {
      dataType: Array.isArray(data) ? "array" : typeof data,
      length: Array.isArray(data) ? data.length : "N/A",
      sample: Array.isArray(data) && data.length > 0 ? data[0] : data,
      isEmpty: !data || (Array.isArray(data) && data.length === 0),
    });
  }
};

// Utility to validate and sanitize chart data
const validateChartData = (
  data: any[],
  requiredFields: string[] = []
): boolean => {
  if (!Array.isArray(data) || data.length === 0) {
    return false;
  }

  // Check if all required fields exist in at least one data point
  if (requiredFields.length > 0) {
    return data.some((item) =>
      requiredFields.every(
        (field) => item && typeof item === "object" && field in item
      )
    );
  }

  return true;
};

interface AttendanceTrendChartProps {
  data: AttendanceTrendData[];
  className?: string;
}

export function AttendanceTrendChart({
  data,
  className,
}: AttendanceTrendChartProps) {
  const { isRTL } = useRTL();

  // Debug the incoming data
  React.useEffect(() => {
    debugChartData("AttendanceTrendChart", data);
  }, [data]);

  // Validate and prepare data with comprehensive error handling
  const chartData = React.useMemo(() => {
    // Early return for invalid data
    if (!data || !Array.isArray(data)) {
      console.warn("[AttendanceTrendChart] Invalid data type:", typeof data);
      return [];
    }

    if (data.length === 0) {
      console.info("[AttendanceTrendChart] Empty data array");
      return [];
    }

    // Validate required fields
    const requiredFields = ["date", "attendanceRate"];
    if (!validateChartData(data, requiredFields)) {
      console.error(
        "[AttendanceTrendChart] Missing required fields:",
        requiredFields
      );
      return [];
    }

    // Transform and sanitize data
    const transformedData = data
      .filter((item) => item && typeof item === "object") // Filter out null/undefined items
      .map((item, index) => {
        const sanitized = {
          date: item.date || `day-${index}`,
          present: Math.max(0, Number(item.present) || 0),
          absent: Math.max(0, Number(item.absent) || 0),
          total: Math.max(0, Number(item.total) || 0),
          attendanceRate: Math.min(
            100,
            Math.max(0, Number(item.attendanceRate) || 0)
          ),
          dayOfWeek: item.dayOfWeek || "",
        };

        // Ensure total is consistent
        if (
          sanitized.total === 0 &&
          (sanitized.present > 0 || sanitized.absent > 0)
        ) {
          sanitized.total = sanitized.present + sanitized.absent;
        }

        // Recalculate attendance rate if needed
        if (sanitized.total > 0 && sanitized.attendanceRate === 0) {
          sanitized.attendanceRate = Math.round(
            (sanitized.present / sanitized.total) * 100
          );
        }

        return sanitized;
      })
      .filter((item) => item.total > 0 || item.attendanceRate > 0); // Remove empty data points

    console.log(
      "[AttendanceTrendChart] Processed data:",
      transformedData.length,
      "items"
    );
    return transformedData;
  }, [data]);

  const formatDate = (dateStr: string) => {
    try {
      return format(new Date(dateStr), "dd/MM", { locale: ar });
    } catch {
      return dateStr;
    }
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) {
      return null;
    }

    try {
      const data = payload[0]?.payload;
      if (!data) return null;

      const formattedDate = (() => {
        try {
          return format(new Date(label), "EEEE، dd MMMM yyyy", { locale: ar });
        } catch {
          return label || "تاريخ غير محدد";
        }
      })();

      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{formattedDate}</p>
          <div className="space-y-1 mt-2">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm">حاضر: {data.present || 0}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span className="text-sm">غائب: {data.absent || 0}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-sm">
                معدل الحضور: {data.attendanceRate || 0}%
              </span>
            </div>
            {data.total && (
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                <span className="text-sm">المجموع: {data.total}</span>
              </div>
            )}
          </div>
        </div>
      );
    } catch (error) {
      console.error("[AttendanceTrendChart] Tooltip error:", error);
      return null;
    }
  };

  // Show loading state if no data
  if (chartData.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-blue-600" />
            اتجاه الحضور عبر الزمن
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            لا توجد بيانات لعرضها
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5 text-blue-600" />
          اتجاه الحضور عبر الزمن
          {process.env.NODE_ENV === "development" && (
            <span className="text-xs text-gray-500">
              ({chartData.length} نقطة)
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="w-full h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={chartData}
              margin={{
                top: 10,
                right: isRTL ? 10 : 30,
                left: isRTL ? 30 : 10,
                bottom: 0,
              }}
            >
              <defs>
                <linearGradient
                  id="attendanceGradient"
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1} />
                </linearGradient>
              </defs>
              <CartesianGrid
                strokeDasharray="3 3"
                stroke="#e5e7eb"
                opacity={0.5}
              />
              <XAxis
                dataKey="date"
                tickFormatter={formatDate}
                reversed={isRTL}
                tick={{ fontSize: 12 }}
                axisLine={{ stroke: "#d1d5db" }}
                tickLine={{ stroke: "#d1d5db" }}
              />
              <YAxis
                domain={[0, 100]}
                tick={{ fontSize: 12 }}
                axisLine={{ stroke: "#d1d5db" }}
                tickLine={{ stroke: "#d1d5db" }}
                label={{
                  value: "معدل الحضور (%)",
                  angle: isRTL ? 90 : -90,
                  position: isRTL ? "insideRight" : "insideLeft",
                  style: { textAnchor: "middle" },
                }}
              />
              <Tooltip
                content={<CustomTooltip />}
                cursor={{
                  stroke: "#3b82f6",
                  strokeWidth: 1,
                  strokeDasharray: "3 3",
                }}
              />
              <Area
                type="monotone"
                dataKey="attendanceRate"
                stroke="#3b82f6"
                strokeWidth={2}
                fillOpacity={1}
                fill="url(#attendanceGradient)"
                dot={{ fill: "#3b82f6", strokeWidth: 2, r: 4 }}
                activeDot={{
                  r: 6,
                  stroke: "#3b82f6",
                  strokeWidth: 2,
                  fill: "#ffffff",
                }}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}

interface AttendanceComparisonChartProps {
  data: UserAttendanceAnalytics[];
  title?: string;
  className?: string;
}

export function AttendanceComparisonChart({
  data,
  title = "مقارنة معدلات الحضور",
  className,
}: AttendanceComparisonChartProps) {
  const { isRTL } = useRTL();

  // Debug the incoming data
  React.useEffect(() => {
    debugChartData("AttendanceComparisonChart", data);
    console.log("[AttendanceComparisonChart] Title:", title);
  }, [data, title]);

  // Validate and prepare data with comprehensive error handling
  const chartData = React.useMemo(() => {
    // Early return for invalid data
    if (!data || !Array.isArray(data)) {
      console.warn(
        "[AttendanceComparisonChart] Invalid data type:",
        typeof data
      );
      return [];
    }

    if (data.length === 0) {
      console.info("[AttendanceComparisonChart] Empty data array");
      return [];
    }

    // Validate required fields
    const requiredFields = ["user", "attendanceRate"];
    if (!validateChartData(data, requiredFields)) {
      console.error(
        "[AttendanceComparisonChart] Missing required fields:",
        requiredFields
      );
      return [];
    }

    // Transform and sanitize data
    const transformedData = data
      .filter((item) => item && typeof item === "object" && item.user)
      .slice(0, 10) // Take top 10 users for better visualization
      .map((ua, index) => {
        const userName = ua.user?.name || `مستخدم ${index + 1}`;
        const sanitized = {
          name:
            userName.length > 15 ? userName.substring(0, 15) + "..." : userName,
          fullName: userName,
          attendanceRate: Math.min(
            100,
            Math.max(0, Number(ua.attendanceRate) || 0)
          ),
          totalSessions: Math.max(0, Number(ua.totalSessions) || 0),
          presentSessions: Math.max(0, Number(ua.presentSessions) || 0),
          absentSessions: Math.max(0, Number(ua.absentSessions) || 0),
          riskLevel: ua.riskLevel || "unknown",
          userId: ua.user?.id || `user-${index}`,
        };

        // Ensure data consistency
        if (sanitized.totalSessions === 0 && sanitized.presentSessions > 0) {
          sanitized.totalSessions =
            sanitized.presentSessions + sanitized.absentSessions;
        }

        return sanitized;
      })
      .filter((item) => item.totalSessions > 0 || item.attendanceRate > 0); // Remove users with no data

    console.log(
      "[AttendanceComparisonChart] Processed data:",
      transformedData.length,
      "users"
    );
    return transformedData;
  }, [data]);

  const getBarColor = (riskLevel: string) => {
    switch (riskLevel) {
      case "low":
        return "#10b981";
      case "medium":
        return "#f59e0b";
      case "high":
        return "#ef4444";
      case "critical":
        return "#dc2626";
      default:
        return "#6b7280";
    }
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.fullName}</p>
          <div className="space-y-1 mt-2">
            <p className="text-sm">معدل الحضور: {data.attendanceRate}%</p>
            <p className="text-sm">الجلسات الحاضرة: {data.presentSessions}</p>
            <p className="text-sm">إجمالي الجلسات: {data.totalSessions}</p>
            <Badge
              variant={data.riskLevel === "low" ? "default" : "destructive"}
              className="text-xs"
            >
              {data.riskLevel === "low"
                ? "منخفض المخاطر"
                : data.riskLevel === "medium"
                ? "متوسط المخاطر"
                : data.riskLevel === "high"
                ? "عالي المخاطر"
                : "حرج"}
            </Badge>
          </div>
        </div>
      );
    }
    return null;
  };

  // Show loading state if no data
  if (chartData.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-green-600" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            لا توجد بيانات لعرضها
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-green-600" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="w-full h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={chartData}
              margin={{
                top: 20,
                right: isRTL ? 10 : 30,
                left: isRTL ? 30 : 10,
                bottom: 80,
              }}
            >
              <CartesianGrid
                strokeDasharray="3 3"
                stroke="#e5e7eb"
                opacity={0.5}
              />
              <XAxis
                dataKey="name"
                angle={-45}
                textAnchor="end"
                height={80}
                reversed={isRTL}
                tick={{ fontSize: 11 }}
                axisLine={{ stroke: "#d1d5db" }}
                tickLine={{ stroke: "#d1d5db" }}
                interval={0}
              />
              <YAxis
                domain={[0, 100]}
                tick={{ fontSize: 12 }}
                axisLine={{ stroke: "#d1d5db" }}
                tickLine={{ stroke: "#d1d5db" }}
                label={{
                  value: "معدل الحضور (%)",
                  angle: isRTL ? 90 : -90,
                  position: isRTL ? "insideRight" : "insideLeft",
                  style: { textAnchor: "middle" },
                }}
              />
              <Tooltip
                content={<CustomTooltip />}
                cursor={{ fill: "rgba(0, 0, 0, 0.1)" }}
              />
              <Bar
                dataKey="attendanceRate"
                radius={[4, 4, 0, 0]}
                fill={(entry: any, index: number) => {
                  const item = chartData[index];
                  return item ? getBarColor(item.riskLevel) : "#6b7280";
                }}
              >
                {chartData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={getBarColor(entry.riskLevel)}
                  />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}

interface CollegeComparisonChartProps {
  data: CollegeComparisonData[];
  className?: string;
}

export function CollegeComparisonChart({
  data,
  className,
}: CollegeComparisonChartProps) {
  const COLORS = [
    "#3b82f6",
    "#10b981",
    "#f59e0b",
    "#ef4444",
    "#8b5cf6",
    "#f97316",
  ];

  // Debug the incoming data
  React.useEffect(() => {
    debugChartData("CollegeComparisonChart", data);
  }, [data]);

  // Validate and prepare data with comprehensive error handling
  const pieData = React.useMemo(() => {
    // Early return for invalid data
    if (!data || !Array.isArray(data)) {
      console.warn("[CollegeComparisonChart] Invalid data type:", typeof data);
      return [];
    }

    if (data.length === 0) {
      console.info("[CollegeComparisonChart] Empty data array");
      return [];
    }

    // Validate required fields
    const requiredFields = ["displayName", "attendanceRate"];
    if (!validateChartData(data, requiredFields)) {
      console.error(
        "[CollegeComparisonChart] Missing required fields:",
        requiredFields
      );
      return [];
    }

    // Transform and sanitize data
    const transformedData = data
      .filter((item) => item && typeof item === "object")
      .map((college, index) => {
        const sanitized = {
          name: college.displayName || `كلية ${index + 1}`,
          value: Math.min(
            100,
            Math.max(0, Number(college.attendanceRate) || 0)
          ),
          totalUsers: Math.max(0, Number(college.totalUsers) || 0),
          topPerformers: Math.max(0, Number(college.topPerformers) || 0),
          needsAttention: Math.max(0, Number(college.needsAttention) || 0),
          color: COLORS[index % COLORS.length],
          category: college.category || "unknown",
        };

        return sanitized;
      })
      .filter((item) => item.totalUsers > 0 || item.value > 0); // Remove empty colleges

    console.log(
      "[CollegeComparisonChart] Processed data:",
      transformedData.length,
      "colleges"
    );
    return transformedData;
  }, [data]);

  const CustomTooltip = ({ active, payload }: any) => {
    if (!active || !payload || !payload.length) {
      return null;
    }

    try {
      const data = payload[0]?.payload;
      if (!data) return null;

      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.name}</p>
          <div className="space-y-1 mt-2">
            <p className="text-sm">معدل الحضور: {data.value}%</p>
            <p className="text-sm">إجمالي الأعضاء: {data.totalUsers}</p>
            <p className="text-sm">المتميزون: {data.topPerformers}</p>
            <p className="text-sm">يحتاج متابعة: {data.needsAttention}</p>
            <div className="flex items-center gap-2 mt-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: data.color }}
              />
              <span className="text-xs text-gray-500">
                {((data.value / 100) * 360).toFixed(1)}° من الدائرة
              </span>
            </div>
          </div>
        </div>
      );
    } catch (error) {
      console.error("[CollegeComparisonChart] Tooltip error:", error);
      return null;
    }
  };

  const renderCustomLabel = ({
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    percent,
  }: any) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? "start" : "end"}
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  // Show loading state if no data
  if (pieData.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChartIcon className="h-5 w-5 text-purple-600" />
            مقارنة الكليات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            لا توجد بيانات لعرضها
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <PieChartIcon className="h-5 w-5 text-purple-600" />
          مقارنة الكليات
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="w-full h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomLabel}
                outerRadius={100}
                innerRadius={0}
                fill="#8884d8"
                dataKey="value"
                stroke="#ffffff"
                strokeWidth={2}
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip
                content={<CustomTooltip />}
                cursor={{ fill: "rgba(255, 255, 255, 0.1)" }}
              />
              <Legend
                verticalAlign="bottom"
                height={36}
                iconType="circle"
                wrapperStyle={{
                  paddingTop: "20px",
                  fontSize: "12px",
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Legend with additional info */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          {data.map((college, index) => (
            <div
              key={college.category}
              className="flex items-center gap-3 p-3 border rounded-lg"
            >
              <div
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: COLORS[index % COLORS.length] }}
              />
              <div className="flex-1">
                <p className="font-medium text-sm">{college.displayName}</p>
                <p className="text-xs text-muted-foreground">
                  {college.totalUsers} عضو • {college.attendanceRate}%
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

interface WeeklyTrendChartProps {
  data: UserAttendanceAnalytics[];
  className?: string;
}

export function WeeklyTrendChart({ data, className }: WeeklyTrendChartProps) {
  const { isRTL } = useRTL();

  // Debug the incoming data
  React.useEffect(() => {
    debugChartData("WeeklyTrendChart", data);
  }, [data]);

  // Validate and prepare data with comprehensive error handling
  const trendData = React.useMemo(() => {
    // Early return for invalid data
    if (!data || !Array.isArray(data)) {
      console.warn("[WeeklyTrendChart] Invalid data type:", typeof data);
      return [];
    }

    if (data.length === 0) {
      console.info("[WeeklyTrendChart] Empty data array");
      return [];
    }

    // Calculate trend distribution
    const trends = [
      {
        trend: "تحسن",
        count: data.filter((ua) => ua.weeklyTrend?.trend === "improving")
          .length,
        color: "#10b981",
        description: "أعضاء يتحسن حضورهم",
      },
      {
        trend: "مستقر",
        count: data.filter((ua) => ua.weeklyTrend?.trend === "stable").length,
        color: "#6b7280",
        description: "أعضاء بحضور مستقر",
      },
      {
        trend: "تراجع",
        count: data.filter((ua) => ua.weeklyTrend?.trend === "declining")
          .length,
        color: "#ef4444",
        description: "أعضاء يتراجع حضورهم",
      },
    ];

    // Filter out trends with zero count for cleaner visualization
    const filteredTrends = trends.filter((trend) => trend.count > 0);

    console.log("[WeeklyTrendChart] Processed trends:", filteredTrends);
    return filteredTrends.length > 0 ? trends : []; // Return all trends even if some are zero for consistency
  }, [data]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.trend}</p>
          <p className="text-sm">عدد الأعضاء: {data.count}</p>
        </div>
      );
    }
    return null;
  };

  // Show loading state if no data
  if (trendData.length === 0 || trendData.every((item) => item.count === 0)) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            الاتجاهات الأسبوعية
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[250px] text-muted-foreground">
            لا توجد بيانات لعرضها
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-blue-600" />
          الاتجاهات الأسبوعية
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="w-full h-[250px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={trendData}
              margin={{
                top: 20,
                right: isRTL ? 10 : 30,
                left: isRTL ? 30 : 10,
                bottom: 20,
              }}
            >
              <CartesianGrid
                strokeDasharray="3 3"
                stroke="#e5e7eb"
                opacity={0.5}
              />
              <XAxis
                dataKey="trend"
                reversed={isRTL}
                tick={{ fontSize: 12 }}
                axisLine={{ stroke: "#d1d5db" }}
                tickLine={{ stroke: "#d1d5db" }}
              />
              <YAxis
                tick={{ fontSize: 12 }}
                axisLine={{ stroke: "#d1d5db" }}
                tickLine={{ stroke: "#d1d5db" }}
                label={{
                  value: "عدد الأعضاء",
                  angle: isRTL ? 90 : -90,
                  position: isRTL ? "insideRight" : "insideLeft",
                  style: { textAnchor: "middle" },
                }}
              />
              <Tooltip
                content={<CustomTooltip />}
                cursor={{ fill: "rgba(0, 0, 0, 0.1)" }}
              />
              <Bar dataKey="count" radius={[4, 4, 0, 0]}>
                {trendData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Summary cards */}
        <div className="grid grid-cols-3 gap-3 mt-4">
          {trendData.map((item) => (
            <div key={item.trend} className="text-center p-3 border rounded-lg">
              <div className="text-2xl font-bold" style={{ color: item.color }}>
                {item.count}
              </div>
              <div className="text-sm text-muted-foreground">{item.trend}</div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

interface RiskLevelDistributionProps {
  data: UserAttendanceAnalytics[];
  className?: string;
}

export function RiskLevelDistribution({
  data,
  className,
}: RiskLevelDistributionProps) {
  // Debug the incoming data
  React.useEffect(() => {
    debugChartData("RiskLevelDistribution", data);
  }, [data]);

  // Validate and prepare data with comprehensive error handling
  const riskData = React.useMemo(() => {
    // Early return for invalid data
    if (!data || !Array.isArray(data)) {
      console.warn("[RiskLevelDistribution] Invalid data type:", typeof data);
      return [];
    }

    if (data.length === 0) {
      console.info("[RiskLevelDistribution] Empty data array");
      return [];
    }

    const risks = [
      {
        level: "منخفض",
        count: data.filter((ua) => ua.riskLevel === "low").length,
        color: "#10b981",
        percentage: 0,
        description: "مخاطر منخفضة",
      },
      {
        level: "متوسط",
        count: data.filter((ua) => ua.riskLevel === "medium").length,
        color: "#f59e0b",
        percentage: 0,
        description: "مخاطر متوسطة",
      },
      {
        level: "عالي",
        count: data.filter((ua) => ua.riskLevel === "high").length,
        color: "#ef4444",
        percentage: 0,
        description: "مخاطر عالية",
      },
      {
        level: "حرج",
        count: data.filter((ua) => ua.riskLevel === "critical").length,
        color: "#dc2626",
        percentage: 0,
        description: "مخاطر حرجة",
      },
    ];

    // Calculate percentages
    const total = data.length;
    risks.forEach((item) => {
      item.percentage = total > 0 ? Math.round((item.count / total) * 100) : 0;
    });

    console.log("[RiskLevelDistribution] Processed risk data:", risks);
    return risks;
  }, [data]);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5 text-orange-600" />
          توزيع مستويات المخاطر
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {riskData.map((item) => (
            <div key={item.level} className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">{item.level}</span>
                <span className="text-sm text-muted-foreground">
                  {item.count} ({item.percentage}%)
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="h-2 rounded-full transition-all duration-500"
                  style={{
                    width: `${item.percentage}%`,
                    backgroundColor: item.color,
                  }}
                />
              </div>
            </div>
          ))}
        </div>

        {/* Summary */}
        <div className="mt-6 p-4 bg-muted rounded-lg">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-600">
                {riskData[0].count + riskData[1].count}
              </div>
              <div className="text-sm text-muted-foreground">حالة جيدة</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">
                {riskData[2].count + riskData[3].count}
              </div>
              <div className="text-sm text-muted-foreground">يحتاج متابعة</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
